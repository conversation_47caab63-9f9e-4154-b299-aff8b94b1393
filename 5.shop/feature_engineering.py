"""
Feature Engineering Utilities for E-commerce Dataset
====================================================

This module contains comprehensive feature engineering functions for the shop dataset.
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer


def create_interaction_features(df):
    """
    Create interaction features between important variables
    """
    df = df.copy()
    
    # Page value interactions
    df['PgVal_x_ProductPages'] = df['PgVal'] * df['Prd']
    df['PgVal_x_TotalDuration'] = df['PgVal'] * (df['AdmDur'] + df['InfDur'] + df['PrdDur'])
    
    # Bounce rate interactions
    df['BncRt_x_ProductPages'] = df['BncRt'] * df['Prd']
    df['BncRt_x_Weekend'] = df['BncRt'] * df['Wkd']
    
    # Special day interactions
    df['SpclDay_x_PgVal'] = df['SpclDay'] * df['PgVal']
    df['SpclDay_x_ProductPages'] = df['SpclDay'] * df['Prd']
    
    return df


def create_binned_features(df):
    """
    Create binned versions of continuous features
    """
    df = df.copy()
    
    # Bin page values into categories
    df['PgVal_Binned'] = pd.cut(df['PgVal'], 
                               bins=[-np.inf, 0, 5, 20, np.inf], 
                               labels=['Zero', 'Low', 'Medium', 'High'])
    
    # Bin total duration
    total_duration = df['AdmDur'] + df['InfDur'] + df['PrdDur']
    df['Duration_Binned'] = pd.cut(total_duration,
                                  bins=5,
                                  labels=['VeryShort', 'Short', 'Medium', 'Long', 'VeryLong'])
    
    # Bin bounce rate
    df['BounceRate_Binned'] = pd.cut(df['BncRt'],
                                    bins=[0, 0.2, 0.5, 0.8, 1.0],
                                    labels=['Low', 'Medium', 'High', 'VeryHigh'])
    
    return df


def create_advanced_features(df):
    """
    Create advanced behavioral and temporal features
    """
    df = df.copy()
    
    # User journey complexity
    df['JourneyComplexity'] = (df['Adm'] > 0).astype(int) + \
                             (df['Inf'] > 0).astype(int) + \
                             (df['Prd'] > 0).astype(int)
    
    # Page transition patterns
    df['AdminToProduct'] = np.where((df['Adm'] > 0) & (df['Prd'] > 0), 1, 0)
    df['InfoToProduct'] = np.where((df['Inf'] > 0) & (df['Prd'] > 0), 1, 0)
    
    # Time efficiency metrics
    total_pages = df['Adm'] + df['Inf'] + df['Prd']
    total_duration = df['AdmDur'] + df['InfDur'] + df['PrdDur']
    
    df['TimeEfficiency'] = np.where(total_duration > 0, 
                                   total_pages / total_duration, 0)
    
    # Value per time spent
    df['ValuePerTime'] = np.where(total_duration > 0,
                                 df['PgVal'] / total_duration, 0)
    
    # Seasonal shopping patterns
    df['IsBlackFriday'] = ((df['Mo'] == 11) & (df['SpclDay'] > 0)).astype(int)
    df['IsChristmas'] = ((df['Mo'] == 12) & (df['SpclDay'] > 0)).astype(int)
    
    return df


def create_statistical_features(df, group_cols=['OS', 'Bsr', 'Rgn']):
    """
    Create statistical features based on groupings
    """
    df = df.copy()
    
    for col in group_cols:
        if col in df.columns:
            # Mean page value by group
            group_mean = df.groupby(col)['PgVal'].transform('mean')
            df[f'{col}_AvgPgVal'] = group_mean
            
            # User's deviation from group average
            df[f'{col}_PgVal_Deviation'] = df['PgVal'] - group_mean
            
            # Group bounce rate
            group_bounce = df.groupby(col)['BncRt'].transform('mean')
            df[f'{col}_AvgBounceRate'] = group_bounce
    
    return df


def get_feature_importance_proxy(X_transformed, y, feature_names):
    """
    Calculate a simple correlation-based feature importance
    """
    correlations = []
    for i in range(X_transformed.shape[1]):
        corr = np.corrcoef(X_transformed[:, i], y)[0, 1]
        correlations.append(abs(corr) if not np.isnan(corr) else 0)
    
    feature_importance = pd.DataFrame({
        'feature': feature_names,
        'importance': correlations
    }).sort_values('importance', ascending=False)
    
    return feature_importance


def create_comprehensive_features(df):
    """
    Apply all feature engineering techniques
    """
    print("Starting comprehensive feature engineering...")
    
    # Apply all feature engineering steps
    df = create_interaction_features(df)
    print("✓ Interaction features created")
    
    df = create_binned_features(df)
    print("✓ Binned features created")
    
    df = create_advanced_features(df)
    print("✓ Advanced behavioral features created")
    
    df = create_statistical_features(df)
    print("✓ Statistical group features created")
    
    print(f"Feature engineering complete. Total features: {df.shape[1]}")
    return df


def get_preprocessing_pipeline(numerical_features, categorical_features):
    """
    Create a comprehensive preprocessing pipeline
    """
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', StandardScaler(), numerical_features),
            ('cat', OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore'), 
             categorical_features)
        ],
        remainder='passthrough'
    )
    
    return preprocessor


# Feature selection utilities
def select_top_features(X_transformed, y, feature_names, top_k=50):
    """
    Select top k features based on correlation with target
    """
    feature_importance = get_feature_importance_proxy(X_transformed, y, feature_names)
    top_features = feature_importance.head(top_k)['feature'].tolist()
    
    # Get indices of top features
    feature_indices = [feature_names.index(feat) for feat in top_features if feat in feature_names]
    
    return X_transformed[:, feature_indices], top_features


def print_feature_summary(df_original, df_engineered, X_transformed, feature_names):
    """
    Print a comprehensive summary of feature engineering results
    """
    print("\n" + "="*60)
    print("FEATURE ENGINEERING SUMMARY")
    print("="*60)
    print(f"Original features: {df_original.shape[1]}")
    print(f"After feature engineering: {df_engineered.shape[1]}")
    print(f"After preprocessing: {X_transformed.shape[1]}")
    print(f"Total samples: {X_transformed.shape[0]}")
    print("\nNew features created:")
    
    original_cols = set(df_original.columns)
    new_cols = set(df_engineered.columns) - original_cols
    for col in sorted(new_cols):
        print(f"  • {col}")
    
    print(f"\nTotal new features: {len(new_cols)}")
    print("="*60)
