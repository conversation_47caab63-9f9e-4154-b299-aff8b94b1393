# Feature Engineering for E-commerce Dataset

This directory contains comprehensive feature engineering solutions for the e-commerce dataset with the following features:

## Dataset Overview

**Original Features:**
- **Administrative Pages**: `Adm`, `AdmDur` - Number and duration of administrative pages
- **Informational Pages**: `Inf`, `InfDur` - Number and duration of informational pages  
- **Product Pages**: `Prd`, `PrdDur` - Number and duration of product pages
- **Behavior Metrics**: `BncRt` (Bounce Rate), `ExtRt` (Exit Rate), `PgVal` (Page Values)
- **Temporal**: `Mo` (Month), `Wkd` (Weekend), `SpclDay` (Special Day)
- **Categorical**: `OS` (Operating System), `Bsr` (Browser), `Rgn` (Region), `TfcTp` (Traffic Type), `VstTp` (Visitor Type)
- **Target**: `Rev` (Revenue - Binary classification)

## Feature Engineering Techniques Implemented

### 1. Basic Aggregation Features
```python
# Total metrics
TotalPages = Adm + Inf + Prd
TotalDuration = AdmDur + InfDur + PrdDur
AvgTimePerPage = TotalDuration / TotalPages
```

### 2. Ratio Features
```python
# Page type ratios
AdminRatio = Adm / TotalPages
InfoRatio = Inf / TotalPages
ProductRatio = Prd / TotalPages

# Duration ratios
AdminDurRatio = AdmDur / TotalDuration
InfoDurRatio = InfDur / TotalDuration
ProductDurRatio = PrdDur / TotalDuration
```

### 3. Engagement Metrics
```python
# Combined engagement score
EngagementScore = PgVal * (1 - BncRt) * (1 - ExtRt)
BounceExitDiff = BncRt - ExtRt
```

### 4. Interaction Features
```python
# Cross-products of important variables
PgVal_x_ProductPages = PgVal * Prd
PgVal_x_TotalDuration = PgVal * TotalDuration
BncRt_x_ProductPages = BncRt * Prd
SpclDay_x_PgVal = SpclDay * PgVal
```

### 5. Binned/Categorical Features
```python
# Discretize continuous variables
PgVal_Binned = pd.cut(PgVal, bins=[-inf, 0, 5, 20, inf], 
                      labels=['Zero', 'Low', 'Medium', 'High'])
Duration_Binned = pd.cut(TotalDuration, bins=5)
BounceRate_Binned = pd.cut(BncRt, bins=[0, 0.2, 0.5, 0.8, 1.0])
```

### 6. Behavioral Pattern Features
```python
# User journey complexity
JourneyComplexity = (Adm > 0) + (Inf > 0) + (Prd > 0)

# Page transition patterns
AdminToProduct = (Adm > 0) & (Prd > 0)
InfoToProduct = (Inf > 0) & (Prd > 0)

# User segments
HighValueVisitor = PgVal > quantile(0.75)
QuickBrowser = TotalDuration < quantile(0.25)
DeepBrowser = TotalPages > quantile(0.75)
```

### 7. Temporal Features
```python
# Seasonal patterns
Quarter = ((Mo - 1) // 3) + 1
IsHolidaySeason = (Mo == 11) | (Mo == 12)
IsBlackFriday = (Mo == 11) & (SpclDay > 0)
IsChristmas = (Mo == 12) & (SpclDay > 0)
```

### 8. Efficiency Metrics
```python
# Time and value efficiency
TimeEfficiency = TotalPages / TotalDuration
ValuePerTime = PgVal / TotalDuration
```

### 9. Statistical Group Features
```python
# Group-based statistics
OS_AvgPgVal = groupby('OS')['PgVal'].transform('mean')
OS_PgVal_Deviation = PgVal - OS_AvgPgVal
Bsr_AvgBounceRate = groupby('Bsr')['BncRt'].transform('mean')
```

## Files Description

### Core Files
- **`main.py`** - Original script with basic feature engineering
- **`main_enhanced.py`** - Complete pipeline with comprehensive feature engineering
- **`feature_engineering.py`** - Utility functions for all feature engineering techniques
- **`feature_engineering_demo.py`** - Step-by-step demonstration of each technique

### Usage

1. **Quick Demo**: Run the demonstration to see each technique in action
```bash
python feature_engineering_demo.py
```

2. **Basic Pipeline**: Run the original enhanced script
```bash
python main.py
```

3. **Complete Pipeline**: Run the comprehensive feature engineering pipeline
```bash
python main_enhanced.py
```

## Key Benefits of This Approach

### 1. **Comprehensive Coverage**
- Covers all major feature engineering categories
- Preserves feature names for interpretability
- Handles edge cases (division by zero, missing values)

### 2. **Business Logic Integration**
- E-commerce specific features (product focus, seasonal patterns)
- User behavior segmentation
- Journey complexity analysis

### 3. **Technical Best Practices**
- Proper train/test splitting before feature engineering
- Standardized preprocessing pipeline
- Feature selection capabilities
- Correlation-based feature importance

### 4. **Scalability**
- Modular design for easy extension
- Efficient pandas operations
- Memory-conscious implementation

## Feature Engineering Impact

The comprehensive approach typically results in:
- **Original features**: 17
- **After basic engineering**: ~35 features  
- **After comprehensive engineering**: 50-100+ features
- **After preprocessing**: 100-200+ features (due to one-hot encoding)

## Model Performance Considerations

1. **Feature Selection**: Use correlation-based or model-based selection for high-dimensional data
2. **Regularization**: Apply dropout and batch normalization to handle feature complexity
3. **Cross-validation**: Validate feature engineering choices across different data splits
4. **Domain Knowledge**: Continuously refine features based on business understanding

## Next Steps

1. **Experiment with feature combinations**
2. **Try advanced feature selection techniques** (RFE, LASSO, etc.)
3. **Implement automated feature engineering** (Featuretools, tsfresh)
4. **A/B test different feature sets** in production
5. **Monitor feature drift** over time

## Dependencies

```python
pandas>=1.3.0
numpy>=1.21.0
scikit-learn>=1.0.0
tensorflow>=2.8.0
matplotlib>=3.5.0
seaborn>=0.11.0
```

This feature engineering approach transforms your raw e-commerce data into a rich feature set that captures user behavior patterns, temporal trends, and engagement metrics, significantly improving model performance for revenue prediction.
