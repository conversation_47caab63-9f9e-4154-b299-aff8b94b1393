"""
Feature Engineering Demonstration for E-commerce Dataset
=======================================================

This script demonstrates various feature engineering techniques step by step.
Run this to understand what each technique does to your data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def load_and_explore_data():
    """Load and provide basic exploration of the dataset"""
    print("="*60)
    print("LOADING AND EXPLORING THE DATASET")
    print("="*60)
    
    df = pd.read_csv('XY_Shop.csv')
    print(f"Dataset shape: {df.shape}")
    print(f"Missing values: {df.isnull().sum().sum()}")
    print(f"Target variable distribution:")
    print(df['Rev'].value_counts())
    
    # Basic statistics
    print(f"\nBasic statistics for numerical features:")
    numerical_cols = ['Adm', 'AdmDur', 'Inf', 'InfDur', 'Prd', 'PrdDur', 
                     'BncRt', 'ExtRt', 'PgVal', 'SpclDay', 'Mo', 'Wkd']
    print(df[numerical_cols].describe())
    
    return df

def demonstrate_basic_features(df):
    """Demonstrate basic feature engineering"""
    print("\n" + "="*60)
    print("BASIC FEATURE ENGINEERING")
    print("="*60)
    
    df_new = df.copy()
    
    # 1. Aggregation features
    print("1. Creating aggregation features...")
    df_new['TotalPages'] = df_new['Adm'] + df_new['Inf'] + df_new['Prd']
    df_new['TotalDuration'] = df_new['AdmDur'] + df_new['InfDur'] + df_new['PrdDur']
    print(f"   • TotalPages: min={df_new['TotalPages'].min()}, max={df_new['TotalPages'].max()}")
    print(f"   • TotalDuration: min={df_new['TotalDuration'].min():.2f}, max={df_new['TotalDuration'].max():.2f}")
    
    # 2. Ratio features
    print("\n2. Creating ratio features...")
    df_new['AvgTimePerPage'] = np.where(df_new['TotalPages'] > 0, 
                                       df_new['TotalDuration'] / df_new['TotalPages'], 0)
    df_new['ProductRatio'] = np.where(df_new['TotalPages'] > 0, 
                                     df_new['Prd'] / df_new['TotalPages'], 0)
    print(f"   • AvgTimePerPage: mean={df_new['AvgTimePerPage'].mean():.2f}")
    print(f"   • ProductRatio: mean={df_new['ProductRatio'].mean():.3f}")
    
    # 3. Engagement metrics
    print("\n3. Creating engagement metrics...")
    df_new['EngagementScore'] = df_new['PgVal'] * (1 - df_new['BncRt']) * (1 - df_new['ExtRt'])
    print(f"   • EngagementScore: mean={df_new['EngagementScore'].mean():.2f}")
    
    return df_new

def demonstrate_interaction_features(df):
    """Demonstrate interaction feature engineering"""
    print("\n" + "="*60)
    print("INTERACTION FEATURE ENGINEERING")
    print("="*60)
    
    df_new = df.copy()
    
    # Page value interactions
    print("1. Page value interactions...")
    df_new['PgVal_x_ProductPages'] = df_new['PgVal'] * df_new['Prd']
    df_new['PgVal_x_Weekend'] = df_new['PgVal'] * df_new['Wkd']
    
    # Bounce rate interactions
    print("2. Bounce rate interactions...")
    df_new['BncRt_x_ProductPages'] = df_new['BncRt'] * df_new['Prd']
    df_new['BncRt_x_SpecialDay'] = df_new['BncRt'] * df_new['SpclDay']
    
    print("   Interaction features created successfully!")
    return df_new

def demonstrate_binning_features(df):
    """Demonstrate binning/discretization"""
    print("\n" + "="*60)
    print("BINNING/DISCRETIZATION FEATURES")
    print("="*60)
    
    df_new = df.copy()
    
    # Bin page values
    print("1. Binning page values...")
    df_new['PgVal_Binned'] = pd.cut(df_new['PgVal'], 
                                   bins=[-np.inf, 0, 5, 20, np.inf], 
                                   labels=['Zero', 'Low', 'Medium', 'High'])
    print(f"   Page value distribution:")
    print(f"   {df_new['PgVal_Binned'].value_counts()}")
    
    # Bin bounce rate
    print("\n2. Binning bounce rate...")
    df_new['BounceRate_Binned'] = pd.cut(df_new['BncRt'],
                                        bins=[0, 0.2, 0.5, 0.8, 1.0],
                                        labels=['Low', 'Medium', 'High', 'VeryHigh'])
    print(f"   Bounce rate distribution:")
    print(f"   {df_new['BounceRate_Binned'].value_counts()}")
    
    return df_new

def demonstrate_behavioral_features(df):
    """Demonstrate behavioral pattern features"""
    print("\n" + "="*60)
    print("BEHAVIORAL PATTERN FEATURES")
    print("="*60)
    
    df_new = df.copy()
    
    # User journey complexity
    print("1. User journey complexity...")
    df_new['JourneyComplexity'] = (df_new['Adm'] > 0).astype(int) + \
                                 (df_new['Inf'] > 0).astype(int) + \
                                 (df_new['Prd'] > 0).astype(int)
    print(f"   Journey complexity distribution:")
    print(f"   {df_new['JourneyComplexity'].value_counts()}")
    
    # User segments
    print("\n2. User segmentation...")
    df_new['HighValueVisitor'] = (df_new['PgVal'] > df_new['PgVal'].quantile(0.75)).astype(int)
    df_new['QuickBrowser'] = (df_new['AdmDur'] + df_new['InfDur'] + df_new['PrdDur'] < 
                             (df_new['AdmDur'] + df_new['InfDur'] + df_new['PrdDur']).quantile(0.25)).astype(int)
    
    print(f"   High value visitors: {df_new['HighValueVisitor'].sum()} ({df_new['HighValueVisitor'].mean()*100:.1f}%)")
    print(f"   Quick browsers: {df_new['QuickBrowser'].sum()} ({df_new['QuickBrowser'].mean()*100:.1f}%)")
    
    return df_new

def demonstrate_temporal_features(df):
    """Demonstrate temporal feature engineering"""
    print("\n" + "="*60)
    print("TEMPORAL FEATURE ENGINEERING")
    print("="*60)
    
    df_new = df.copy()
    
    # Seasonal features
    print("1. Seasonal features...")
    df_new['Quarter'] = ((df_new['Mo'] - 1) // 3) + 1
    df_new['IsHolidaySeason'] = ((df_new['Mo'] == 11) | (df_new['Mo'] == 12)).astype(int)
    
    print(f"   Quarter distribution:")
    print(f"   {df_new['Quarter'].value_counts().sort_index()}")
    print(f"   Holiday season: {df_new['IsHolidaySeason'].sum()} visits ({df_new['IsHolidaySeason'].mean()*100:.1f}%)")
    
    # Special day analysis
    print("\n2. Special day patterns...")
    df_new['IsSpecialDay'] = (df_new['SpclDay'] > 0).astype(int)
    print(f"   Special day visits: {df_new['IsSpecialDay'].sum()} ({df_new['IsSpecialDay'].mean()*100:.1f}%)")
    
    return df_new

def analyze_feature_impact(df_original, df_engineered):
    """Analyze the impact of feature engineering"""
    print("\n" + "="*60)
    print("FEATURE ENGINEERING IMPACT ANALYSIS")
    print("="*60)
    
    # Remove rows with missing target
    df_clean = df_engineered[~df_engineered['Rev'].isna()].copy()
    
    print(f"Original features: {df_original.shape[1]}")
    print(f"After feature engineering: {df_engineered.shape[1]}")
    print(f"New features created: {df_engineered.shape[1] - df_original.shape[1]}")
    
    # Correlation analysis for new features
    print("\nCorrelation with target variable (top 10 new features):")
    
    original_cols = set(df_original.columns)
    new_cols = [col for col in df_engineered.columns if col not in original_cols and col != 'Rev']
    
    correlations = []
    for col in new_cols:
        if df_clean[col].dtype in ['int64', 'float64']:
            corr = df_clean[col].corr(df_clean['Rev'])
            if not pd.isna(corr):
                correlations.append((col, abs(corr)))
    
    correlations.sort(key=lambda x: x[1], reverse=True)
    
    for i, (feature, corr) in enumerate(correlations[:10]):
        print(f"   {i+1:2d}. {feature:<25} {corr:.4f}")

def main():
    """Main demonstration function"""
    print("E-COMMERCE DATASET FEATURE ENGINEERING DEMONSTRATION")
    print("=" * 80)
    
    # Load and explore data
    df = load_and_explore_data()
    
    # Clean data
    df_clean = df[~df['Rev'].isna()].copy()
    
    # Demonstrate each type of feature engineering
    df_basic = demonstrate_basic_features(df_clean)
    df_interaction = demonstrate_interaction_features(df_basic)
    df_binned = demonstrate_binning_features(df_interaction)
    df_behavioral = demonstrate_behavioral_features(df_binned)
    df_final = demonstrate_temporal_features(df_behavioral)
    
    # Analyze impact
    analyze_feature_impact(df_clean, df_final)
    
    print("\n" + "="*80)
    print("FEATURE ENGINEERING COMPLETE!")
    print("="*80)
    print("Next steps:")
    print("1. Run 'python main_enhanced.py' for the complete ML pipeline")
    print("2. Experiment with different feature combinations")
    print("3. Try feature selection techniques to reduce dimensionality")
    print("4. Consider domain-specific features based on business knowledge")

if __name__ == "__main__":
    main()
