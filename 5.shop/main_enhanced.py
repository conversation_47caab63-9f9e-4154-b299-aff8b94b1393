"""
Enhanced E-commerce Revenue Prediction with Comprehensive Feature Engineering
============================================================================

This script demonstrates advanced feature engineering techniques for the shop dataset.
"""

import pandas as pd
import numpy as np
import tensorflow as tf
import tensorflow.keras as keras
from keras.layers import Flatten, Dense, Dropout, BatchNormalization
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Import our feature engineering utilities
from feature_engineering import (
    create_comprehensive_features, 
    get_preprocessing_pipeline,
    print_feature_summary,
    get_feature_importance_proxy,
    select_top_features
)

# Load and examine the data
print("Loading dataset...")
df_XY = pd.read_csv('XY_Shop.csv')
print(f"Dataset shape: {df_XY.shape}")
print(f"Missing values in target: {df_XY['Rev'].isna().sum()}")

# Define original feature groups
numerical_features = ['Adm', 'AdmDur', 'Inf', 'InfDur', 'Prd', 'PrdDur', 
                     'BncRt', 'ExtRt', 'PgVal', 'SpclDay', 'Mo', 'Wkd']
categorical_features = ['OS', 'Bsr', 'Rgn', 'TfcTp', 'VstTp']

print(f"Original numerical features: {len(numerical_features)}")
print(f"Original categorical features: {len(categorical_features)}")

# Clean data - remove rows with missing target values
Xy = df_XY[~df_XY.Rev.isna()].copy()
print(f"Clean dataset shape: {Xy.shape}")
print(f"Target distribution:\n{Xy['Rev'].value_counts()}")

# Apply basic feature engineering from main.py
def create_basic_engineered_features(df):
    """Create the basic engineered features from main.py"""
    df = df.copy()
    
    # Total pages and duration features
    df['TotalPages'] = df['Adm'] + df['Inf'] + df['Prd']
    df['TotalDuration'] = df['AdmDur'] + df['InfDur'] + df['PrdDur']
    
    # Average time per page (avoid division by zero)
    df['AvgTimePerPage'] = np.where(df['TotalPages'] > 0, 
                                   df['TotalDuration'] / df['TotalPages'], 0)
    
    # Page type ratios
    df['AdminRatio'] = np.where(df['TotalPages'] > 0, df['Adm'] / df['TotalPages'], 0)
    df['InfoRatio'] = np.where(df['TotalPages'] > 0, df['Inf'] / df['TotalPages'], 0)
    df['ProductRatio'] = np.where(df['TotalPages'] > 0, df['Prd'] / df['TotalPages'], 0)
    
    # Duration ratios
    df['AdminDurRatio'] = np.where(df['TotalDuration'] > 0, df['AdmDur'] / df['TotalDuration'], 0)
    df['InfoDurRatio'] = np.where(df['TotalDuration'] > 0, df['InfDur'] / df['TotalDuration'], 0)
    df['ProductDurRatio'] = np.where(df['TotalDuration'] > 0, df['PrdDur'] / df['TotalDuration'], 0)
    
    # Engagement metrics
    df['EngagementScore'] = df['PgVal'] * (1 - df['BncRt']) * (1 - df['ExtRt'])
    df['BounceExitDiff'] = df['BncRt'] - df['ExtRt']
    
    # Time-based features
    df['IsWeekend'] = df['Wkd']
    df['IsSpecialDay'] = (df['SpclDay'] > 0).astype(int)
    
    # Seasonal features
    df['Quarter'] = ((df['Mo'] - 1) // 3) + 1
    df['IsHolidaySeason'] = ((df['Mo'] == 11) | (df['Mo'] == 12)).astype(int)
    
    # User behavior patterns
    df['HighValueVisitor'] = (df['PgVal'] > df['PgVal'].quantile(0.75)).astype(int)
    df['QuickBrowser'] = (df['TotalDuration'] < df['TotalDuration'].quantile(0.25)).astype(int)
    df['DeepBrowser'] = (df['TotalPages'] > df['TotalPages'].quantile(0.75)).astype(int)
    
    return df

# Apply basic feature engineering
print("\nApplying basic feature engineering...")
Xy_basic = create_basic_engineered_features(Xy)

# Apply comprehensive feature engineering
print("\nApplying comprehensive feature engineering...")
Xy_engineered = create_comprehensive_features(Xy_basic)

# Identify all numerical features (original + engineered)
basic_new_features = [
    'TotalPages', 'TotalDuration', 'AvgTimePerPage',
    'AdminRatio', 'InfoRatio', 'ProductRatio',
    'AdminDurRatio', 'InfoDurRatio', 'ProductDurRatio',
    'EngagementScore', 'BounceExitDiff', 'Quarter',
    'IsSpecialDay', 'IsHolidaySeason', 'HighValueVisitor',
    'QuickBrowser', 'DeepBrowser'
]

# Get all numerical columns (excluding categorical and target)
all_numerical_cols = []
for col in Xy_engineered.columns:
    if col not in categorical_features + ['Rev'] and Xy_engineered[col].dtype in ['int64', 'float64']:
        all_numerical_cols.append(col)

# Update categorical features to include binned features
binned_categorical_features = ['PgVal_Binned', 'Duration_Binned', 'BounceRate_Binned']
all_categorical_features = categorical_features + binned_categorical_features

# Remove binned features from numerical list if they exist
all_numerical_cols = [col for col in all_numerical_cols if col not in binned_categorical_features]

print(f"\nTotal numerical features: {len(all_numerical_cols)}")
print(f"Total categorical features: {len(all_categorical_features)}")

# Separate features and target
X = Xy_engineered.drop('Rev', axis=1)
y = Xy_engineered['Rev']

# Split the data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"\nTraining set: {X_train.shape}")
print(f"Test set: {X_test.shape}")

# Create and apply preprocessing pipeline
print("\nApplying preprocessing pipeline...")
preprocessor = get_preprocessing_pipeline(all_numerical_cols, all_categorical_features)

# Fit and transform the data
X_train_transformed = preprocessor.fit_transform(X_train)
X_test_transformed = preprocessor.transform(X_test)

# Get feature names after transformation
def get_feature_names_comprehensive(preprocessor, numerical_features, categorical_features):
    """Get feature names after ColumnTransformer preprocessing"""
    feature_names = []
    
    # Numerical features
    feature_names.extend(numerical_features)
    
    # Categorical features (one-hot encoded)
    if hasattr(preprocessor.named_transformers_['cat'], 'get_feature_names_out'):
        cat_feature_names = preprocessor.named_transformers_['cat'].get_feature_names_out(categorical_features)
        feature_names.extend(cat_feature_names)
    
    return feature_names

feature_names = get_feature_names_comprehensive(preprocessor, all_numerical_cols, all_categorical_features)

# Print comprehensive summary
print_feature_summary(Xy, Xy_engineered, X_train_transformed, feature_names)

# Feature selection (optional - select top 100 features)
print(f"\nOriginal feature count: {X_train_transformed.shape[1]}")
if X_train_transformed.shape[1] > 100:
    print("Selecting top 100 features based on correlation with target...")
    X_train_selected, selected_features = select_top_features(
        X_train_transformed, y_train, feature_names, top_k=100
    )
    X_test_selected = X_test_transformed[:, [feature_names.index(feat) for feat in selected_features]]
    
    X_train_final = X_train_selected
    X_test_final = X_test_selected
    print(f"Selected features: {X_train_final.shape[1]}")
else:
    X_train_final = X_train_transformed
    X_test_final = X_test_transformed
    selected_features = feature_names

print(f"Final feature count for modeling: {X_train_final.shape[1]}")

# Build and train the enhanced model
print("\nBuilding enhanced neural network model...")

tf.random.set_seed(42)
Init = keras.initializers.RandomNormal(seed=42)

# Enhanced model with regularization
model = keras.models.Sequential([
    Flatten(input_shape=[X_train_final.shape[1]], name='input'),

    # First hidden layer with batch normalization and dropout
    Dense(256, activation="relu", kernel_initializer=Init, name='hidden1'),
    BatchNormalization(),
    Dropout(0.3),

    # Second hidden layer
    Dense(128, activation="relu", kernel_initializer=Init, name='hidden2'),
    BatchNormalization(),
    Dropout(0.3),

    # Third hidden layer
    Dense(64, activation="relu", kernel_initializer=Init, name='hidden3'),
    BatchNormalization(),
    Dropout(0.2),

    # Fourth hidden layer
    Dense(32, activation="relu", kernel_initializer=Init, name='hidden4'),
    Dropout(0.2),

    # Output layer
    Dense(1, activation='sigmoid', kernel_initializer=Init, name='output')
])

model.summary()

# Compile with advanced optimizer settings
model.compile(
    optimizer=keras.optimizers.Adam(learning_rate=0.001),
    loss="binary_crossentropy",
    metrics=["accuracy", "precision", "recall"]
)

# Add callbacks for better training
callbacks = [
    keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
    keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5, min_lr=1e-6)
]

# Train the model
print("\nTraining the model...")
history = model.fit(
    X_train_final, y_train,
    epochs=100,
    validation_data=(X_test_final, y_test),
    batch_size=512,
    callbacks=callbacks,
    verbose=1
)

# Evaluate the model
print("\nEvaluating the model...")
test_results = model.evaluate(X_test_final, y_test, verbose=0)
print(f"Test Loss: {test_results[0]:.4f}")
print(f"Test Accuracy: {test_results[1]:.4f}")
print(f"Test Precision: {test_results[2]:.4f}")
print(f"Test Recall: {test_results[3]:.4f}")

# Generate predictions and detailed evaluation
y_pred_proba = model.predict(X_test_final)
y_pred = (y_pred_proba > 0.5).astype(int).flatten()

print("\nDetailed Classification Report:")
print(classification_report(y_test, y_pred))

print("\nConfusion Matrix:")
cm = confusion_matrix(y_test, y_pred)
print(cm)

# Feature importance analysis
print("\nTop 20 Most Important Features (by correlation):")
feature_importance = get_feature_importance_proxy(X_train_final, y_train, selected_features)
print(feature_importance.head(20).to_string(index=False))

print("\n" + "="*80)
print("FEATURE ENGINEERING IMPACT ANALYSIS")
print("="*80)
print("This enhanced model includes:")
print("• Basic engineered features (ratios, totals, averages)")
print("• Interaction features (cross-products of important variables)")
print("• Binned categorical features (discretized continuous variables)")
print("• Advanced behavioral features (journey complexity, efficiency metrics)")
print("• Statistical group features (group averages and deviations)")
print("• Temporal features (seasonal patterns, special events)")
print("• User segmentation features (high-value, quick browsers, etc.)")
print("="*80)
